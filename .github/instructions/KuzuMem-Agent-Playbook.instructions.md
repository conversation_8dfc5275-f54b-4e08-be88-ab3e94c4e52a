---
applyTo: '-'
---
## 1 · Core Rules
1. **Graph is truth.** Treat the KuzuMem knowledge graph (Repository ➜ Metadata ➜ Context ➜ Component ➜ Decision ➜ Rule) as the single source of facts.  
2. **Branch first.** Pass `branch` in **every** tool call; use `main` for shared wisdom, feature branches for WIP.  
3. **When in doubt, “use context7”.** If you are unsure about an API, package feature, or while adding/upgrading a dependency, force MCP through **Context7** to inject live, version-specific docs.  
4. **Capture & cite.** Write new memories immediately after discoveries; include date (`YYYY-MM-DD`) and rationale to meet ADR best practice.  
5. **Prefer concise summaries (<300 chars) and descriptive IDs** (`comp-AuthService`, `rule-Logging-Std`).  

## 2 · Quick-Start Tool Cheatsheet
| Action | Call | Trigger |
| ------ | ---- | ------- |
| 🆕 New repo | `init-memory-bank` | First interaction |
| 📄 Read meta | `get-metadata` | Session start |
| ✏️ Update meta | `update-metadata` | Scope/tech change |
| 🔍 Recall | `get-context latest:true` | Begin coding |
| ➕ Log insight | `update-context` | After analysis/discussion |
| 🏗️ Add component | `add-component` | Discover/create module |
| 🗳️ Record decision | `add-decision` | Choose arch/tech |
| 📜 Add rule | `add-rule` | Define standard |
| 🔗 Deps → | `get-component-dependencies` | Pre-refactor |
| ← Deps | `get-component-dependents` | Impact check |
| ⚖️ Gov. check | `get-governing-items-for-component` | Before code gen |

Other graph algorithms (`mcp_pagerank`, `louvain-community-detection`, `k-core-decomposition`, `strongly-connected-components`, `shortest-path`) are **analysis helpers**—call them when architecture insight is needed and surface only the top findings.

## 3 · Write vs Read
* **Write** memories when you observe patterns, finish a significant task, or make a decision.  
* **Read** memories at session start, before large recommendations, and whenever you need historical context.  
* Always log after generating final code so future sessions understand context.

## 4 · Graph Algo Play (when architecture matters)
1. `mcp_pagerank` → list top central components; refactor or harden them first.  
2. `louvain-community-detection` → propose module boundaries that maximize cohesion.  
3. `k-core-decomposition` → reveal tightly coupled cores; consider decoupling.  
4. `strongly-connected-components` → spot cycles, then suggest break-up strategies.  
5. `shortest-path` → explain indirect dependency chains to the user.

## 5 · Progressive Ops
Long-running algos?  
* Notify user: “Running PageRank, may take 30 s…”  
* Stream partial results when available, then summarize.

## 6 · Import / Export
* Offer **template exports** for repeatable project types; verify `init-memory-bank` exists before `import`.  
* Keep branch parity between export ↔ import to avoid cross-contamination.  