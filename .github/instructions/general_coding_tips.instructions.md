---
applyTo: '**/*.ts'
---
# Copilot Instructions for this Repository
This monorepo hosts a GenAI-powered web application:

* **Server-side** — Google **Genkit** (Node.js 1.0) AI flows, Develop AI agents, RAG and other GenAI features.
* **Front-end** — **Next.js 15** App Router with React Server Components, TypeScript, Tailwind CSS, and shadcn/ui.  

## Coding conventions
1. **TypeScript first.** All new code must be `.ts`/`.tsx`; no JS files. Provide strict-mode types.  
2. **ESLint / Prettier**: follow the config in `/config/eslint-next.cjs`; two-space indent, single quotes.  
3. **Module boundaries**: co-locate feature code under `src/(feature)/`; avoid deep relative imports (`../../../`).  
4. **React components**: use functional components with hooks; prefer server components unless client interactivity is required.  
5. **Styling**: use Tailwind utility classes; never write raw CSS files.  
6. **Images**: import via `next/image`; set `priority` only for LCP candidates; optimise widths.

## Genkit-specific rules
1. Flows live in `ai/flows/` and are exported via `export const flow = defineFlow(...)`.  
2. Use **Gemini 2.5-Pro** (`googleAI.model('gemini-2.5-pro')`) by default; switch to `gpt-4.1` only if latency requires it.  
3. Persist vector embeddings in in-memory vector DB.
4. **Tracing is automatic.** Genkit records an OpenTelemetry trace for every flow run; no extra `observe()` or manual instrumentation is needed.  
5. Expose flows to the front end through **server actions** in `app/api/**/route.ts`, wrapping handlers with `genkitEndpoint()`.  .

## Testing & quality
1. Write **Vitest** unit tests next to code (`*.test.ts`), aiming for ≥ 80 % coverage.  
2. Use **Playwright** for end-to-end routes `/chat` and `/admin`.  
3. When suggesting code snippets, prefer patterns that pass the existing tests.
4. Always use **better-sse** for streaming responses.

## Documentation style
* When Copilot answers architecture or onboarding questions, respond in **GitHub-flavoured Markdown**.  
* Prefix CLI commands with `$`; include expected output snippets in triple-backtick blocks.

## Tone
* Answer in concise English.  
* Where relevant, reference official docs links (firebase.google.com, nextjs.org, genkit.dev) for further reading.