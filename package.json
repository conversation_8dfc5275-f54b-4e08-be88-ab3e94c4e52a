{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@genkit-ai/core": "^1.7.0", "@genkit-ai/dev-local-vectorstore": "^1.7.0", "@genkit-ai/express": "^1.7.0", "@genkit-ai/flow": "^0.5.17", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.7.0", "@genkit-ai/vertexai": "^1.7.0", "@hookform/resolvers": "^4.1.3", "@opentelemetry/exporter-jaeger": "^2.0.0", "@papra/lecture": "^0.0.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@tavily/core": "^0.5.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "destr": "^2.0.5", "firebase": "^11.3.0", "flatted": "^3.3.3", "fs": "^0.0.1-security", "genkit": "^1.8.0", "genkitx-anthropic": "^0.22.0", "genkitx-mcp": "^1.7.0", "genkitx-openai": "^0.22.3", "llm-chunk": "^0.0.1", "lucide-react": "^0.475.0", "mermaid": "^11.6.0", "net": "^1.0.2", "next": "^15.3.2", "next-themes": "^0.4.6", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-pdf-highlighter": "^8.0.0-rc.0", "recharts": "^2.15.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tavily": "^1.0.2", "tls": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/pdfjs-dist": "^2.10.377", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "9.26.0", "eslint-config-next": "15.3.2", "genkit-cli": "^1.8.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5"}}